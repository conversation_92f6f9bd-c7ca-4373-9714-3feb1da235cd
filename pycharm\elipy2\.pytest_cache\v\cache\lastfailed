{"elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_list_value": true, "elipy2/tests/test_expire.py::TestExpire::test_keep_n_at_path_dry_run": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_dict_format": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_string_format": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_empty_list_value": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_invalid_count_values": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_invalid_string_format": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_mixed_formats": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_case_insensitive": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_exception_handling": true, "elipy2/tests/test_expire.py::TestExpire::test_save_tagged_builds": true, "elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_usrpass_wrong_creds_keys_in_ess": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_skip_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_no_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_local": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_already_deployed": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_custom_source": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_with_tnt": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_with_tnt_overwrite": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_tool": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_tnt": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_artifact_fb2019_with_tnt": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_artifact_fb2019_tool": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_custom_binaries_destination": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_tool_tool_targets_override": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_platform_tool_targets_override": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_no_licensee": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_multi_licensee": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_single_licensee": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_not_use_fbcli_custom_tag": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_custom_tag": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_mirror_false": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_artifact_override": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_no_licensee": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_multi_licensee": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_single_licensee": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_not_use_fbcli_custom_tag": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_custom_tag": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_artifact_override": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_artifact_fb2019_tool_deploy_frostedtests": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_tnt_use_fbenv_copy": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_fbenv_args": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_fbenv_args_with_tests": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_fbenv_args_overwrite_check": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_file_issue": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_deploy_tests_file_issue": true, "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build": true, "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_zip": true, "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt": true, "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt_nomaster": true, "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt_nobilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt_no_cl": true, "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_not_there": true, "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_http_error": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_ant_local_build": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_ant_local_build_no_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_ant_local_build_exception": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_nomaster": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_zip": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_zip_failure": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_failure": true, "elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build": true, "elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build_plt": true, "elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build_plt_no_cl": true, "elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build_no_dir": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_state": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_state_no_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state_no_dir": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state_no_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state_use_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_no_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_fb2019_no_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_artifact_fb2019_no_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_fb2019_no_bilbo_fbenv_args": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_fb2019_no_bilbo_fetch_tests": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_tool": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_custom_destination": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_with_unknown_platform": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_not_use_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build_dest": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build_by_source": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build_by_source_dest": true, "elipy2/tests/test_filer.py::TestFiler::test_create_combine_stream_info_file": true, "elipy2/tests/test_filer.py::TestFiler::test_create_combine_stream_info_file_deployed": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_no_bilbo": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_custom_source": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_no_source_specified": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_dest_exists_exception": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_combined": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_with_combine_params": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_delta_bundles": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_delta_bundles_non_default_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_delta_bundles_deployed": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_head_bundles": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_head_bundles_non_default_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_head_bundles_deployed": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_combine_output": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_combine_output_non_default_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_combine_output_deployed": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_state": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_state_non_default_bundle_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_deploy_state_deployed": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_state": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_state_non_default_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_delta_bundles": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_delta_bundles_no_dest": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_head_bundles": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_head_bundles_non_default_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_state_no_dest": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_dest": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_non_default_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_dest_non_default_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head_no_dest": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head_non_default_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head_no_dest_non_default_dir_name": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_win64_chunkmanifest": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_win64_chunkmanifest_failure_no_destination": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_win64_chunkmanifest_combine": true, "elipy2/tests/test_filer.py::TestFiler::test_baseline_xb1_layout": true, "elipy2/tests/test_filer.py::TestFiler::test_baseline_xb1_layout_no_dest_exception": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xb1_old": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xb1_new": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xbsx": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xb1_no_dest_exception": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_wrong_platform": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xbsx_exception_file_not_found": true, "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xbsx_combine": true, "elipy2/tests/test_filer.py::TestFiler::test_baseline_ps4_package": true, "elipy2/tests/test_filer.py::TestFiler::test_baseline_ps_package": true, "elipy2/tests/test_filer.py::TestFiler::test_baseline_ps5_package": true, "elipy2/tests/test_filer.py::TestFiler::test_baseline_ps5_package_combine": true}