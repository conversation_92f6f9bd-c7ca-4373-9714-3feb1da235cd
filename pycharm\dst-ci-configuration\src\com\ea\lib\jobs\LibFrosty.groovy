package com.ea.lib.jobs

import com.ea.lib.Compare
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl

class LibFrosty {
    /**
     * Adds generic job parameters for frosty start jobs.
     */
    static void frosty_start(def job, def project, def branchInfo) {
        final MODIFIERS = ['frosty']
        final DISABLE_BUILD = LibCommonNonCps.get_setting_value(branchInfo, MODIFIERS, 'disable_build', false)
        final MAIN_UNVERIFIED_BRANCH = branchInfo.main_unverified_branch ?: false
        final ONLY_BUILD_ON_NEW_CODE = branchInfo.frosty_only_build_on_new_code ?: false
        final int PARALLEL_LIMIT_FROSTY = branchInfo.parallel_limit_frosty ?: 1

        def frostyReferenceJob = LibCommonNonCps.get_setting_value(
            branchInfo, MODIFIERS, 'frosty_reference_job', '.data.start', project
        )
        if (frostyReferenceJob.startsWith('.')) {
            frostyReferenceJob = branchInfo.branch_name + frostyReferenceJob
        }

        def cleanDataDefault = ['False', 'True']
        if (LibCommonNonCps.get_setting_value(branchInfo, MODIFIERS, 'clean_data', false, project) == true) {
            cleanDataDefault = cleanDataDefault.reverse()
        }

        final ENVIRONMENT_VARIABLES_TO_ADD = [
            branch_name                  : branchInfo.branch_name,
            code_branch                  : branchInfo.code_branch,
            combine_reference_job        : branchInfo.combine_bundles?.combine_reference_job ?: '',
            data_branch                  : branchInfo.data_branch,
            data_folder                  : branchInfo.data_folder,
            dataset                      : branchInfo.dataset,
            frostbite_syncer_setup       : project.frostbite_syncer_setup ?: false,
            frosty_quick_job             : branchInfo.frosty_quick_job ?: '',
            frosty_reference_job         : frostyReferenceJob,
            linux_docker_images          : branchInfo.linux_docker_images ?: false,
            main_unverified_branch       : MAIN_UNVERIFIED_BRANCH,
            frosty_only_build_on_new_code: ONLY_BUILD_ON_NEW_CODE,
            non_virtual_data_branch      : branchInfo.non_virtual_data_branch ?: '',
            non_virtual_data_folder      : branchInfo.non_virtual_data_folder ?: '',
            project_name                 : project.name,
            retry_limit                  : LibCommonNonCps.get_setting_value(branchInfo, MODIFIERS, 'retry_limit', 1, project),
            enable_lkg_p4_counters       : branchInfo.enable_lkg_p4_counters ?: false,
        ]
        final TRIGGER_STRING = branchInfo.trigger_string_frosty ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
        final TRIGGER_TYPE = branchInfo.trigger_type_frosty ?: 'none'

        // Add sections to the Jenkins job.
        job.with {
            description("Scheduler to orchestrate game build packaging on ${branchInfo.branch_name} .")
            disabled(DISABLE_BUILD)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                throttleConcurrentBuilds {
                    maxTotal(PARALLEL_LIMIT_FROSTY)
                }
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (TRIGGER_TYPE == 'scm') {
                            pollSCM {
                                scmpoll_spec(TRIGGER_STRING)
                            }
                        } else if (TRIGGER_TYPE == 'cron') {
                            cron {
                                spec(TRIGGER_STRING)
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', cleanDataDefault, 'If True, Avalanche will be cleaned at the beginning of the run.')
                if (MAIN_UNVERIFIED_BRANCH || ONLY_BUILD_ON_NEW_CODE) {
                    booleanParam('build_without_new_code', false, ' Only build when we have new code')
                }
            }
            environmentVariables {
                ENVIRONMENT_VARIABLES_TO_ADD.each { variable, value ->
                    env(variable, value)
                }
            }
        }
    }

    /**
     * Adds generic job parameters for patchfrosty start jobs.
     */
    static void patchfrosty_start(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['patchfrosty']
        def combine_reference_job = branch_info.combine_bundles?.combine_reference_job ?: ''
        def enable_lkg_p4_counters = branch_info.enable_lkg_p4_counters ?: false
        def linux_docker_images = branch_info.linux_docker_images ?: false
        def patch_branch = branch_info.patch_branch ?: branch_info.data_branch
        def retry_limit = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'retry_limit', 1, project)
        def shift_every_build = branch_info.shift_every_build ?: false
        def frosty_reference_job = LibCommonNonCps.get_setting_value(
            branch_info, modifiers, 'frosty_reference_job', '.patchdata.start', project
        )
        if (frosty_reference_job.startsWith('.')) {
            frosty_reference_job = branch_info.branch_name + frosty_reference_job
        }
        def clean_data_default = ['False', 'True']
        if (LibCommonNonCps.get_setting_value(branch_info, modifiers, 'clean_data', false, project) == true) {
            clean_data_default = clean_data_default.reverse()
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Scheduler to orchestrate game build packaging on ' + branch_info.branch_name + '.')
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', clean_data_default, 'If True, Avalanche will be cleaned at the beginning of the run. This only applies to frosty jobs, if any such are triggered by this job.')
            }
            environmentVariables {
                env('branch_name', branch_info.branch_name)
                env('code_branch', branch_info.code_branch)
                env('combine_reference_job', combine_reference_job)
                env('data_branch', branch_info.data_branch)
                env('dataset', branch_info.dataset)
                env('enable_lkg_p4_counters', enable_lkg_p4_counters)
                env('frosty_reference_job', frosty_reference_job)
                env('linux_docker_images', linux_docker_images)
                env('patch_branch', patch_branch)
                env('project_name', project.name)
                env('retry_limit', retry_limit)
                env('shift_every_build', shift_every_build)
            }
        }
    }

    /**
     * Adds generic job parameters for frosty build jobs.
     */
    static void frosty_job(def job, def project, def branch_info, def variant) {
        def modifiers = ['frosty', branch_info.platform, variant.format, variant.config]
        // Set values for variables.
        def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', project)
        def variant_args = variant.args ?: ''
        def variant_statebuild = variant.statebuild != null ? variant.statebuild : true

        def disable_frosty_symbol_upload = branch_info.disable_frosty_symbol_upload ?: false
        def dry_run_frosty = branch_info.dry_run_frosty ?: false
        def enable_eac = LibCommonNonCps.get_setting_value(branch_info, [branch_info.platform, variant.format], 'enable_eac', false, project)
        def frostbite_licensee = branch_info.frostbite_licensee
        def skip_streaming_install_package = branch_info.skip_streaming_install_package ?: false
        def use_linuxclient = branch_info.use_linuxclient ?: false
        def use_super_bundles = branch_info.deployment_data_branch ?: branch_info.use_super_bundles ?: false
        def use_win64trial = branch_info.use_win64trial ?: false
        def use_recompression_cache = LibCommonNonCps.get_setting_value(branch_info, [branch_info.platform], 'use_recompression_cache', false, project)
        def clean_master_version_check = LibCommonNonCps.get_setting_value(branch_info, [branch_info.platform], 'clean_master_version_check', false, project)
        def file_hashes = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'file_hashes', false, project)
        def import_avalanche = true
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_fb_settings', [:], project)
        def is_patchdata_stream = branch_info.is_patchdata_stream ?: false
        def poolbuild_label = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'poolbuild_label', 'poolbuild', project)
        // Check for job_label_poolbuild in master settings
        String job_label_poolbuild = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'job_label_poolbuild', null, project)
        def is_virtual_stream = LibCommonNonCps.get_setting_value(branch_info, [], 'is_virtual_stream', false)
        def combine_settings_file = LibCommonNonCps.get_setting_value(branch_info, [branch_info.platform], 'combine_settings_file', null, project)
        def parallel_limit_frosty = branch_info.parallel_limit_frosty ?: 1

        def elipy_format = variant.format
        def steam_build = elipy_format.contains('steam')
        def use_combine_bundles = elipy_format.contains('combine')
        def use_oreans = branch_info.oreans_protection ?: false
        elipy_format = (elipy_format.contains('combine') || elipy_format.contains('steam')) ? 'digital' : elipy_format

        if (variant_statebuild == false || branch_info.statebuild_frosty == false) {
            import_avalanche = false
            job_label = branch_info.data_branch + ' && frosty && ' +
                branch_info.platform + ' && ' +
                variant.format + ' && ' + variant.config
        } else if (branch_info.poolbuild_frosty) {
            // Use job_label_poolbuild if defined, otherwise use poolbuild_label
            job_label = job_label_poolbuild ?: poolbuild_label
            job_label += ' && ' + branch_info.platform
        }
        if (steam_build) {
            job_label = branch_info.data_branch + ' && frosty && ' +
                branch_info.platform + ' && steam_build &&' +
                variant.config
        }

        def asset = branch_info.frosty_asset ?: branch_info.asset
        if (branch_info.platform == 'server' || branch_info.platform == 'linuxserver') {
            asset = branch_info.frosty_server_asset ?: branch_info.server_asset
        } else if (elipy_format == 'digital') {
            asset = branch_info.frosty_digital_asset ?: branch_info.asset
        }

        def timeout_hours_frosty = LibCommonNonCps.get_setting_value(
            branch_info, modifiers, 'timeout_hours_frosty', 3, project
        )
        def timeout_minutes = timeout_hours_frosty * 60
        def extraFrostyArgs = LibCommonNonCps.get_setting_value(
            branch_info, modifiers, 'extra_frosty_args', [], project
        )
        String extra_args = extraFrostyArgs.join(' ') + variant_args

        if (use_combine_bundles) {
            // Check if separate combined bundles job is enabled
            def use_separate_combined_job = branch_info.combine_bundles?.use_separate_combined_job ?: false

            if (use_separate_combined_job) {
                // Use pre-created combined bundles from separate job
                extra_args += ' --use-combine-bundles true --use-precreated-combined-bundles'
            } else {
                // Create combined bundles inline (existing behavior)
                extra_args += ' --use-combine-bundles true' +
                    ' --combine-code-branch ' + branch_info.combine_bundles.source_branch_code +
                    ' --combine-code-changelist %combine_code_changelist%' +
                    ' --combine-data-branch ' + branch_info.combine_bundles.source_branch_data +
                    ' --combine-data-changelist %combine_data_changelist%'
                if (combine_settings_file) {
                    extra_args += ' --combine-settings-file ' + combine_settings_file
                }
            }
        } else {
            extra_args += ' --content-layer %content_layer% '
        }
        if (use_oreans == true) {
            extra_args += ' --use-oreans '
        }
        if (steam_build) {
            extra_args += ' --steam-build true'
        }

        if (is_patchdata_stream && branch_info.platform == 'linux64') {
            use_super_bundles = false
        }

        String elipy_call = branch_info.elipy_call
        if (is_virtual_stream) {
            String fbenvconfigservice_call = [
                'cli.bat',
                'x64',
                '&&',
                'fb',
                'fbenvconfigservice',
                'config',
                '-o',
                branch_info.data_branch,
                'FB_BRANCH_ID',
            ].join(' ')
            elipy_call = elipy_call.replace('cli.bat x64', fbenvconfigservice_call)
        }

        [
            '--dry-run'                                          : dry_run_frosty,
            '--import-avalanche-state'                           : import_avalanche,
            '--use-linuxclient'                                  : use_linuxclient,
            '--disable-frosty-symbol-upload'                     : disable_frosty_symbol_upload,
            '--use-deployed-bundles'                             : use_super_bundles,
            '--use-win64trial'                                   : use_win64trial && variant.format != 'files' && variant.config != 'performance',
            ('--licensee ' + frostbite_licensee)                 : frostbite_licensee != null,
            '--email %monkey_email% --password "%monkey_passwd%"': user_credentials != '',
            '--skip-streaming-install-package'                   : skip_streaming_install_package,
            '--use-recompression-cache'                          : use_recompression_cache,
            '--enable-eac'                                       : enable_eac,
            '--clean-master-version-check'                       : clean_master_version_check,
            // Temporarily stop this since expression debug data is not working when emitting superbundles
            // '--expression-debug-data'                            : branch_info.expression_debug_data,
            '--keep-intermediate-data'                           : branch_info.keep_intermediate_data,
            '--file-hashes true'                                 : file_hashes && variant.format == 'files',
            '--virtual-branch-override true'                     : is_virtual_stream,
        ].each { arg, check -> extra_args += check ? " $arg" : '' }

        // Add sections to the Jenkins job.
        job.with {
            description('Builds and deploys ' + branch_info.dataset + ' for ' + branch_info.platform + ' in ' + variant.format +
                '.' + variant.config + ' for region ' + variant.region + ' with code from ' + branch_info.code_branch + '.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            concurrentBuild()
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(parallel_limit_frosty)
            }
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                if (use_combine_bundles) {
                    stringParam {
                        name('combine_code_changelist')
                        defaultValue('')
                        description('Specifies code changelist for the branch to combine bundles from.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_data_changelist')
                        defaultValue('')
                        description('Specifies data changelist for the branch to combine bundles from.')
                        trim(true)
                    }
                } else {
                    // content layers do not work with combined bundles
                    stringParam {
                        name('content_layer')
                        defaultValue('Source')
                        description('Specifies the content layer to cook. Leave as "Source" if cooking the base layer')
                        trim(true)
                    }
                }
                choiceParam('clean_data', ['False', 'True'], 'If True, Avalanche will be cleaned before the build.')
            }
            environmentVariables {
                env('combine_code_branch', branch_info.combine_bundles?.source_branch_code ?: '')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                if (use_combine_bundles) {
                    buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}.${ENV, var="combine_code_branch"}.${ENV, var="combine_code_changelist"}.${ENV, var="combine_data_changelist"}')
                } else {
                    buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                }
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if ((project.vault_server_credentials != '') && (Compare.containsIgnoreCase(branch_info.platform, 'server'))) {
                        string(project.vault_server_variable, project.vault_server_credentials)
                    } else if (project.vault_credentials != '') {
                        string(project.vault_variable, project.vault_credentials)
                    }
                    if ((project.game_team_secrets_credential != '')) {
                        string(project.game_team_secrets_variable, project.game_team_secrets_credential)
                    }
                    if ((project.game_team_secrets_credential_extra != '')) {
                        string('dice-online-gla-prod-secret-id', project.game_team_secrets_credential_extra)
                    }
                    if ((project.game_team_secrets_credential_online_prod != '')) {
                        string('dice-online-gla-prod-secret-prod-id', project.game_team_secrets_credential_online_prod)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                    if (branch_info.marvin_trigger_upload) {
                        usernamePassword('marvin_upload_user', 'marvin_upload_password', project.external_job.upload_credentials)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(
                    [
                        elipy_call,
                        'frosty',
                        branch_info.platform,
                        elipy_format,
                        variant.config,
                        asset,
                        '--code-branch', branch_info.code_branch,
                        '--code-changelist', '%code_changelist%',
                        '--data-branch', branch_info.data_branch,
                        '--data-changelist', '%data_changelist%',
                        '--data-directory', branch_info.dataset,
                        '--region', variant.region,
                        '--data-clean', '%clean_data%',
                    ].join(' ') + ' ' + extra_args
                )
            }
        }
    }

    /**
     * Adds combined bundles creation jobs that produces combined bundles
     * for a platform which can then be used by frosty/patchfrosty jobs.
     */
    static void combined_bundles_job(def job, def project, def branch_info, String platform) {
        def modifiers = ['combined_bundles', platform]

        // Set values for variables
        def combine_bundles_config = branch_info.combine_bundles
        def poolbuild_label = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'poolbuild_label', 'poolbuild', project)
        def job_label_poolbuild = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'job_label_poolbuild', null, project)
        def timeout_hours = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'timeout_hours_combined_bundles', 2, project)
        def timeout_minutes = timeout_hours * 60
        def combine_settings_file = LibCommonNonCps.get_setting_value(branch_info, [platform], 'combine_settings_file', null, project)

        // Determine job label based on configuration
        def job_label
        def label_type = combine_bundles_config?.combined_job_label_type ?: 'poolbuild'

        if (label_type == 'dedicated') {
            // Use dedicated machine label: {stream_name} combine-bundles {platform}
            job_label = "${branch_info.branch_name} combine-bundles ${platform}"
        } else {
            // Use poolbuild label (default): {poolbuild_label} {platform}
            job_label = job_label_poolbuild ?: poolbuild_label
            job_label += " && ${platform}"
        }

        // Build extra arguments for the combined_bundles script
        String extra_args = ''
        if (combine_settings_file) {
            extra_args += " --combine-settings-file ${combine_settings_file}"
        }

        // Check if delta bundles should be created for patchfrosty streams
        def create_delta_bundles = branch_info.patchfrosty_matrix?.any { it.variants?.any { variant ->
            variant.format?.contains('combine')
        }}
        if (create_delta_bundles) {
            extra_args += ' --create-delta-bundles'
            if (branch_info.first_patch) {
                extra_args += ' --first-patch'
            }
        }

        // Add sections to the Jenkins job
        job.with {
            description("Creates combined bundles for ${platform} platform on ${branch_info.branch_name}.")
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            concurrentBuild()
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(1)
            }
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('combine_code_changelist')
                    defaultValue('')
                    description('Specifies code changelist for the branch to combine bundles from.')
                    trim(true)
                }
                stringParam {
                    name('combine_data_changelist')
                    defaultValue('')
                    description('Specifies data changelist for the branch to combine bundles from.')
                    trim(true)
                }
                if (create_delta_bundles && !branch_info.first_patch) {
                    stringParam {
                        name('patch_code_changelist')
                        defaultValue('')
                        description('Specifies patch baseline code changelist for delta bundle creation.')
                        trim(true)
                    }
                    stringParam {
                        name('patch_data_changelist')
                        defaultValue('')
                        description('Specifies patch baseline data changelist for delta bundle creation.')
                        trim(true)
                    }
                }
                if (create_delta_bundles) {
                    stringParam {
                        name('disc_code_changelist')
                        defaultValue('')
                        description('Specifies disc baseline code changelist for delta bundle creation.')
                        trim(true)
                    }
                    stringParam {
                        name('disc_data_changelist')
                        defaultValue('')
                        description('Specifies disc baseline data changelist for delta bundle creation.')
                        trim(true)
                    }
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}.${ENV, var="combine_code_changelist"}.${ENV, var="combine_data_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                if (project.vault_credentials != '') {
                    credentialsBinding {
                        string(project.vault_variable, project.vault_credentials)
                    }
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)

                // Build the combined_bundles command
                def command_parts = [
                    branch_info.elipy_call,
                    'combined_bundles',
                    platform,
                    '--code-branch', branch_info.code_branch,
                    '--code-changelist', '%code_changelist%',
                    '--data-branch', branch_info.data_branch,
                    '--data-changelist', '%data_changelist%',
                    '--combine-code-branch', combine_bundles_config.source_branch_code,
                    '--combine-code-changelist', '%combine_code_changelist%',
                    '--combine-data-branch', combine_bundles_config.source_branch_data,
                    '--combine-data-changelist', '%combine_data_changelist%',
                    '--data-directory', branch_info.dataset,
                ]

                // Add delta bundle parameters if needed
                if (create_delta_bundles) {
                    command_parts.addAll([
                        '--disc-code-branch', branch_info.code_branch,
                        '--disc-code-changelist', '%disc_code_changelist%',
                        '--disc-data-branch', branch_info.data_branch,
                        '--disc-data-changelist', '%disc_data_changelist%',
                    ])

                    if (!branch_info.first_patch) {
                        command_parts.addAll([
                            '--patch-code-branch', branch_info.code_branch,
                            '--patch-code-changelist', '%patch_code_changelist%',
                            '--patch-data-branch', branch_info.data_branch,
                            '--patch-data-changelist', '%patch_data_changelist%',
                        ])
                    }
                }

                batchFile(command_parts.join(' ') + ' ' + extra_args)
            }
        }
    }

    /**
     * Adds generic job parameters for patchfrosty build jobs.
     */
    static void patchfrosty_job(def job, def project, def branch_info, def variant) {
        def modifiers = [branch_info.platform, variant.format, variant.config, variant.region]
        // Set values for variables.
        def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', project)
        def variant_args = variant.args ?: ''
        def variant_statebuild = variant.statebuild != null ? variant.statebuild : true

        def first_patch = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'first_patch', false)
        def standalone_disc_baseline = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'standalone_disc_baseline', false)
        def standalone_patch_baseline = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'standalone_patch_baseline', false)

        def disable_frosty_symbol_upload = branch_info.disable_frosty_symbol_upload ?: false
        def dry_run_patchfrosty = branch_info.dry_run_patchfrosty ?: false
        def enable_eac = LibCommonNonCps.get_setting_value(branch_info, [branch_info.platform, variant.format], 'enable_eac', false, project)
        def increase_version_by = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'increase_version_by', false, project)
        def frostbite_licensee = branch_info.frostbite_licensee
        def same_baseline_config = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'same_baseline_config', false, project)
        def skip_streaming_install_package = branch_info.skip_streaming_install_package ?: false
        def use_win64trial = branch_info.use_win64trial ?: false
        def use_recompression_cache = LibCommonNonCps.get_setting_value(branch_info, [branch_info.platform], 'use_recompression_cache', false, project)

        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_fb_settings', [:], project)
        def poolbuild_label = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'poolbuild_label', 'poolbuild', project)
        // Check for job_label_poolbuild in master settings
        String job_label_poolbuild = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'job_label_poolbuild', null, project)
        def elipy_format = variant.format
        def steam_build = elipy_format.contains('steam')
        def use_combine_bundles = variant.format.contains('combine')
        def use_oreans = branch_info.oreans_protection ?: false
        def is_virtual_stream = LibCommonNonCps.get_setting_value(branch_info, [], 'is_virtual_stream', false)
        def combine_settings_file = LibCommonNonCps.get_setting_value(branch_info, [branch_info.platform], 'combine_settings_file', null, project)

        if (variant_statebuild == false) {
            job_label = [
                branch_info.data_branch,
                'patchfrosty',
                branch_info.platform,
                variant.format,
                variant.config,
            ].join(' && ')
        } else if (branch_info.poolbuild_patchfrosty == true) {
            // Use job_label_poolbuild if defined, otherwise use poolbuild_label
            job_label = [
                job_label_poolbuild ?: poolbuild_label,
                branch_info.platform,
            ].join(' && ')
        } else if (branch_info.statebuild_patchfrosty == false) {
            job_label = [
                branch_info.data_branch,
                'patchfrosty',
                branch_info.platform,
                variant.format,
                variant.config,
            ].join(' && ')
        }
        if (steam_build) {
            job_label = [
                branch_info.data_branch,
                'patchfrosty',
                branch_info.platform, 'steam_build',
                variant.config,
            ].join(' && ')
        }

        def timeout_hours_frosty = branch_info.timeout_hours_frosty_patchfrosty ?: 4
        def timeout_minutes = timeout_hours_frosty * 60

        String extra_args = branch_info.extra_patchfrosty_args ?: ''
        extra_args += variant_args
        if (increase_version_by != false) {
            extra_args += ' --increase-version-by ' + increase_version_by
        }
        if (dry_run_patchfrosty == true) {
            extra_args += ' --dry-run'
        }
        if (first_patch == true) {
            extra_args += ' --first-patch'
        } else {
            extra_args += ' --patch-code-branch %patch_code_branch% --patch-code-changelist %patch_code_changelist%'
            extra_args += ' --patch-data-branch %patch_data_branch% --patch-data-changelist %patch_data_changelist%'
            if (use_combine_bundles) {
                extra_args += ' --combine-patch-code-branch %combine_patch_code_branch% --combine-patch-code-changelist %combine_patch_code_changelist%'
                extra_args += ' --combine-patch-data-branch %combine_patch_data_branch% --combine-patch-data-changelist %combine_patch_data_changelist%'
            }
        }
        if (standalone_disc_baseline) {
            extra_args += ' --standalone-disc-baseline'
        }
        def non_standalone_patch_baseline = LibCommonNonCps.get_setting_value(branch_info, [], 'non_standalone_patch_baseline', [], project)
        if (standalone_patch_baseline && !(non_standalone_patch_baseline.contains(branch_info.platform))) {
            extra_args += ' --standalone-patch-baseline'
        }
        if (use_win64trial == true && variant.format != 'files') {
            extra_args += ' --use-win64trial'
        }
        if (disable_frosty_symbol_upload == true) {
            extra_args += ' --disable-frosty-symbol-upload'
        }
        if (frostbite_licensee != null) {
            extra_args += ' --licensee ' + frostbite_licensee
        }
        if (skip_streaming_install_package == true) {
            extra_args += ' --skip-streaming-install-package'
        }
        if (user_credentials != '') {
            extra_args += ' --email %monkey_email% --password "%monkey_passwd%"'
        }
        if (use_recompression_cache == true) {
            extra_args += ' --use-recompression-cache'
        }
        if (enable_eac == true) {
            extra_args += ' --enable-eac'
        }
        if (same_baseline_config == true) {
            extra_args += ' --same-baseline-config'
        }
        if (use_combine_bundles) {
            // Check if separate combined bundles job is enabled
            def use_separate_combined_job = branch_info.combine_bundles?.use_separate_combined_job ?: false

            if (use_separate_combined_job) {
                // Use pre-created combined bundles and delta bundles from separate job
                extra_args += ' --use-combine-bundles true --use-precreated-combined-bundles --use-precreated-delta-bundles'
            } else {
                // Create combined bundles inline (existing behavior)
                extra_args += ' --use-combine-bundles true'
                extra_args += ' --combine-code-branch ' + branch_info.combine_bundles.source_branch_code + ' --combine-code-changelist %combine_code_changelist%'
                extra_args += ' --combine-data-branch ' + branch_info.combine_bundles.source_branch_data + ' --combine-data-changelist %combine_data_changelist%'
                extra_args += ' --combine-disc-code-branch %combine_disc_code_branch% --combine-disc-code-changelist %combine_disc_code_changelist%'
                extra_args += ' --combine-disc-data-branch %combine_disc_data_branch% --combine-disc-data-changelist %combine_disc_data_changelist% '
                if (combine_settings_file) {
                    extra_args += ' --combine-settings-file ' + combine_settings_file
                }
            }
        }
        if (use_oreans == true) {
            extra_args += ' --use-oreans '
        }
        if (steam_build) {
            extra_args += ' --steam-build true '
        }
        if (is_virtual_stream) {
            extra_args += ' --virtual-branch-override true'
        }

        String elipy_call = branch_info.elipy_call
        if (is_virtual_stream) {
            String fbenvconfigservice_call = [
                'cli.bat',
                'x64',
                '&&',
                'fb',
                'fbenvconfigservice',
                'config',
                '-o',
                branch_info.data_branch,
                'FB_BRANCH_ID',
            ].join(' ')
            elipy_call = elipy_call.replace('cli.bat x64', fbenvconfigservice_call)
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Builds and deploys patches using ' + branch_info.dataset + ' for ' + branch_info.platform + ' in ' +
                variant.format + '.' + variant.config + ' for region ' + variant.region + ' with code from ' + branch_info.code_branch + '.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('disc_code_branch')
                    defaultValue('')
                    description('Specifies which branch the disc code came from.')
                    trim(true)
                }
                stringParam {
                    name('disc_code_changelist')
                    defaultValue('')
                    description('Specifies which changelist the disc code was built on.')
                    trim(true)
                }
                stringParam {
                    name('disc_data_branch')
                    defaultValue('')
                    description('Specifies which branch the disc data came from.')
                    trim(true)
                }
                stringParam {
                    name('disc_data_changelist')
                    defaultValue('')
                    description('Specifies which changelist the disc data was built on.')
                    trim(true)
                }
                stringParam {
                    name('patch_code_branch')
                    defaultValue('')
                    description('Specifies which branch the patch code came from.')
                    trim(true)
                }
                stringParam {
                    name('patch_code_changelist')
                    defaultValue('')
                    description('Specifies which changelist the patch code was built on.')
                    trim(true)
                }
                stringParam {
                    name('patch_data_branch')
                    defaultValue('')
                    description('Specifies which branch the patch data came from.')
                    trim(true)
                }
                stringParam {
                    name('patch_data_changelist')
                    defaultValue('')
                    description('Specifies which changelist the patch data was built on.')
                    trim(true)
                }
                if (use_combine_bundles) {
                    stringParam {
                        name('combine_code_changelist')
                        defaultValue('""')
                        description('Specifies combined code changelist to sync.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_data_changelist')
                        defaultValue('""')
                        description('Specifies combined data changelist to sync.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_disc_code_branch')
                        defaultValue('""')
                        description('Specifies which combined branch the disc code came from.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_disc_code_changelist')
                        defaultValue('""')
                        description('Specifies which combined changelist the disc code was built on.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_disc_data_branch')
                        defaultValue('""')
                        description('Specifies which combined branch the disc data came from.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_disc_data_changelist')
                        defaultValue('""')
                        description('Specifies which combined changelist the disc data was built on.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_patch_code_branch')
                        defaultValue('""')
                        description('Specifies which combined branch the patch code came from.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_patch_code_changelist')
                        defaultValue('""')
                        description('Specifies which combined changelist the patch code was built on.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_patch_data_branch')
                        defaultValue('""')
                        description('Specifies which combined branch the patch data came from.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_patch_data_changelist')
                        defaultValue('""')
                        description('Specifies which combined changelist the patch data was built on.')
                        trim(true)
                    }
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if ((project.vault_server_credentials != '') && (Compare.containsIgnoreCase(branch_info.platform, 'server'))) {
                        string(project.vault_server_variable, project.vault_server_credentials)
                    } else if (project.vault_credentials != '') {
                        string(project.vault_variable, project.vault_credentials)
                    }
                    if ((project.game_team_secrets_credential != '')) {
                        string(project.game_team_secrets_variable, project.game_team_secrets_credential)
                    }
                    if ((project.game_team_secrets_credential_extra != '')) {
                        string('dice-online-gla-prod-secret-id', project.game_team_secrets_credential_extra)
                    }
                    if ((project.game_team_secrets_credential_online_prod != '')) {
                        string('dice-online-gla-prod-secret-prod-id', project.game_team_secrets_credential_online_prod)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(
                    [
                        elipy_call,
                        'patch_frosty',
                        branch_info.platform,
                        variant.config,
                        '--code-branch', branch_info.code_branch,
                        '--code-changelist', '%code_changelist%',
                        '--data-branch ', branch_info.data_branch,
                        '--data-changelist', '%data_changelist%',
                        '--disc-code-branch', '%disc_code_branch%',
                        '--disc-code-changelist', '%disc_code_changelist%',
                        '--disc-data-branch', '%disc_data_branch%',
                        '--disc-data-changelist', '%disc_data_changelist%',
                        '--data-directory', branch_info.dataset,
                        '--region', variant.region,
                    ].join(' ') + ' ' + extra_args
                )
            }
        }
    }

    /**
     * Add job parameters for a job update p4 counter after frosty.start job is finished with success.
     */
    static void frosty_p4counter_updater(def job, def project, def branch_info) {
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        def p4_code_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_server', '', project)
        def p4_data_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_server', '', project)
        // Add sections to the Jenkins job.
        job.with {
            description('P4 counter update after frosty start job done.')
            label(job_label)
            concurrentBuild()
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(8)
            }
            parameters {
                stringParam {
                    name('code_countername')
                    defaultValue('')
                    description('Specifies p4 code counter name to use.')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist for p4 counter to set value to.')
                    trim(true)
                }
                stringParam {
                    name('data_countername')
                    defaultValue('')
                    description('Specifies p4 data counter name to use.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist for p4 counter to set value to.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}')
                timeout {
                    absolute(100)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' p4_counter' +
                    ' --port ' + p4_code_server +
                    ' --client ' + project.p4_code_client +
                    ' --user ' + project.p4_user_single_slash +
                    ' --countername %code_countername% --value %code_changelist%' +
                    ' --extra-port ' + p4_data_server +
                    ' --extra-client ' + project.p4_data_client +
                    ' --extra-countername %data_countername% --extra-value %data_changelist%')
            }
        }
    }
}
